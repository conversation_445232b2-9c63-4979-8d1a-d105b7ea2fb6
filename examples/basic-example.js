/**
 * 基础示例 - 演示如何使用动态执行系统
 * 这个文件包含了一些常用的 Playwright 操作示例
 */

// 示例1: 导航到网页
const example1_navigate = `
// 导航到指定网页
await page.goto('https://www.baidu.com');
console.log('已导航到百度首页');
`;

// 示例2: 搜索操作
const example2_search = `
// 在搜索框中输入内容并搜索
await page.fill('#kw', 'Playwright 自动化测试');
await page.click('#su');
await page.waitForSelector('.result');
console.log('搜索完成');
`;

// 示例3: 等待元素
const example3_wait = `
// 等待特定元素出现
await page.waitForSelector('.result', { timeout: 5000 });
console.log('搜索结果已加载');
`;

// 示例4: 获取文本内容
const example4_getText = `
// 获取页面标题
const title = await page.title();
console.log('页面标题:', title);

// 获取第一个搜索结果的文本
const firstResult = await page.textContent('.result h3 a');
console.log('第一个搜索结果:', firstResult);
`;

// 示例5: 截图
const example5_screenshot = `
// 截取页面截图
await page.screenshot({ 
  path: 'search-result.png',
  fullPage: true 
});
console.log('截图已保存');
`;

// 示例6: 表单操作
const example6_form = `
// 表单填写示例
await page.goto('https://example.com/form');
await page.fill('input[name="username"]', '测试用户');
await page.fill('input[name="password"]', '123456');
await page.check('input[type="checkbox"]');
await page.selectOption('select[name="country"]', 'China');
await page.click('button[type="submit"]');
console.log('表单提交完成');
`;

// 示例7: 页面交互
const example7_interaction = `
// 页面交互示例
await page.hover('.menu-item');
await page.click('.dropdown-option');
await page.dblclick('.editable-text');
await page.press('input', 'Control+A');
await page.type('input', '新的文本内容');
console.log('页面交互完成');
`;

// 示例8: 条件判断
const example8_conditional = `
// 条件判断示例
const isVisible = await page.isVisible('.error-message');
if (isVisible) {
  const errorText = await page.textContent('.error-message');
  console.log('发现错误信息:', errorText);
} else {
  console.log('没有错误信息');
}

const isEnabled = await page.isEnabled('button.submit');
if (isEnabled) {
  await page.click('button.submit');
  console.log('按钮已点击');
} else {
  console.log('按钮不可用');
}
`;

// 示例9: 循环操作
const example9_loop = `
// 循环操作示例
const items = await page.$$('.list-item');
console.log('找到', items.length, '个列表项');

for (let i = 0; i < items.length; i++) {
  const text = await items[i].textContent();
  console.log('第', i + 1, '项:', text);
  
  if (text.includes('目标内容')) {
    await items[i].click();
    console.log('已点击目标项');
    break;
  }
}
`;

// 示例10: 错误处理
const example10_errorHandling = `
// 错误处理示例
try {
  await page.waitForSelector('.may-not-exist', { timeout: 3000 });
  console.log('元素存在');
} catch (error) {
  console.log('元素不存在，继续执行其他操作');
}

// 使用可选的等待
const element = await page.$('.optional-element');
if (element) {
  await element.click();
  console.log('可选元素已点击');
} else {
  console.log('可选元素不存在，跳过');
}
`;

// 示例11: 多页面操作
const example11_multiPage = `
// 多页面操作示例
const newPage = await context.newPage();
await newPage.goto('https://example.com/page2');
console.log('新页面已打开');

// 在新页面中操作
await newPage.fill('input', '新页面输入');
await newPage.click('button');

// 切换回原页面
await page.bringToFront();
console.log('已切换回原页面');

// 关闭新页面
await newPage.close();
console.log('新页面已关闭');
`;

// 示例12: 文件操作
const example12_fileOperations = `
// 文件操作示例
// 上传文件
await page.setInputFiles('input[type="file"]', 'path/to/file.txt');
console.log('文件已上传');

// 下载文件
const [download] = await Promise.all([
  page.waitForEvent('download'),
  page.click('a[download]')
]);
await download.saveAs('downloaded-file.pdf');
console.log('文件已下载');
`;

// 示例13: 网络请求监控
const example13_networkMonitoring = `
// 网络请求监控示例
page.on('request', request => {
  console.log('请求:', request.url());
});

page.on('response', response => {
  console.log('响应:', response.url(), response.status());
});

await page.goto('https://example.com');
console.log('网络监控已启用');
`;

// 示例14: 性能监控
const example14_performance = `
// 性能监控示例
const startTime = Date.now();

await page.goto('https://example.com');
await page.waitForLoadState('networkidle');

const loadTime = Date.now() - startTime;
console.log('页面加载时间:', loadTime, 'ms');

// 获取性能指标
const metrics = await page.evaluate(() => {
  const navigation = performance.getEntriesByType('navigation')[0];
  return {
    domContentLoaded: navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart,
    loadComplete: navigation.loadEventEnd - navigation.loadEventStart
  };
});
console.log('性能指标:', metrics);
`;

// 示例15: 移动端模拟
const example15_mobile = `
// 移动端模拟示例
await page.setViewportSize({ width: 375, height: 667 });
await page.goto('https://m.example.com');

// 模拟触摸操作
await page.tap('.mobile-button');
await page.swipe('.swipeable-area', { direction: 'left' });

console.log('移动端操作完成');
`;

// 导出所有示例
module.exports = {
  example1_navigate,
  example2_search,
  example3_wait,
  example4_getText,
  example5_screenshot,
  example6_form,
  example7_interaction,
  example8_conditional,
  example9_loop,
  example10_errorHandling,
  example11_multiPage,
  example12_fileOperations,
  example13_networkMonitoring,
  example14_performance,
  example15_mobile,
  
  // 获取所有示例的函数
  getAllExamples() {
    return {
      '导航到网页': example1_navigate,
      '搜索操作': example2_search,
      '等待元素': example3_wait,
      '获取文本内容': example4_getText,
      '截图': example5_screenshot,
      '表单操作': example6_form,
      '页面交互': example7_interaction,
      '条件判断': example8_conditional,
      '循环操作': example9_loop,
      '错误处理': example10_errorHandling,
      '多页面操作': example11_multiPage,
      '文件操作': example12_fileOperations,
      '网络请求监控': example13_networkMonitoring,
      '性能监控': example14_performance,
      '移动端模拟': example15_mobile
    };
  },
  
  // 获取示例描述
  getExampleDescriptions() {
    return {
      '导航到网页': '演示如何导航到指定的网页',
      '搜索操作': '演示如何在搜索框中输入内容并执行搜索',
      '等待元素': '演示如何等待页面元素加载完成',
      '获取文本内容': '演示如何获取页面元素的文本内容',
      '截图': '演示如何截取页面截图',
      '表单操作': '演示如何填写和提交表单',
      '页面交互': '演示各种页面交互操作',
      '条件判断': '演示如何根据页面状态进行条件判断',
      '循环操作': '演示如何对多个元素进行循环操作',
      '错误处理': '演示如何处理可能出现的错误',
      '多页面操作': '演示如何在多个页面之间切换和操作',
      '文件操作': '演示文件上传和下载操作',
      '网络请求监控': '演示如何监控网络请求',
      '性能监控': '演示如何监控页面性能',
      '移动端模拟': '演示如何模拟移动端设备操作'
    };
  }
};
