const DynamicExecutionServer = require('./dynamicExecutionServer');

/**
 * 启动动态执行服务器
 */
async function startServer() {
  try {
    // 从环境变量获取端口，默认为 8081
    const port = process.env.PORT || 8081;

    // 创建服务器实例
    const server = new DynamicExecutionServer(port);

    // 启动服务器
    await server.start();

    console.log('🚀 动态执行服务器启动成功！');
    console.log(`📡 服务地址: http://localhost:${port}`);
    console.log('💡 使用 Ctrl+C 停止服务器');

    // 优雅关闭处理
    process.on('SIGINT', async () => {
      console.log('\n🛑 正在关闭服务器...');
      await server.stop();
      process.exit(0);
    });

    process.on('SIGTERM', async () => {
      console.log('\n🛑 正在关闭服务器...');
      await server.stop();
      process.exit(0);
    });

  } catch (error) {
    console.error('❌ 服务器启动失败:', error);
    process.exit(1);
  }
}

// 启动服务器
startServer();