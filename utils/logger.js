const fs = require('fs');
const path = require('path');

/**
 * 日志工具类
 * 提供结构化的日志记录功能
 */
class Logger {
  constructor(options = {}) {
    this.logLevel = options.logLevel || 'info';
    this.logToFile = options.logToFile || false;
    this.logDir = options.logDir || path.join(__dirname, '../logs');
    this.maxLogFiles = options.maxLogFiles || 10;
    this.maxLogSize = options.maxLogSize || 10 * 1024 * 1024; // 10MB
    
    this.levels = {
      error: 0,
      warn: 1,
      info: 2,
      debug: 3,
      trace: 4
    };

    if (this.logToFile) {
      this.ensureLogDir();
    }
  }

  /**
   * 确保日志目录存在
   */
  ensureLogDir() {
    if (!fs.existsSync(this.logDir)) {
      fs.mkdirSync(this.logDir, { recursive: true });
    }
  }

  /**
   * 获取当前日志文件路径
   */
  getCurrentLogFile() {
    const date = new Date().toISOString().split('T')[0];
    return path.join(this.logDir, `playwright-runner-${date}.log`);
  }

  /**
   * 格式化日志消息
   */
  formatMessage(level, message, meta = {}) {
    const timestamp = new Date().toISOString();
    const metaStr = Object.keys(meta).length > 0 ? ` ${JSON.stringify(meta)}` : '';
    return `[${timestamp}] [${level.toUpperCase()}] ${message}${metaStr}`;
  }

  /**
   * 写入日志到文件
   */
  writeToFile(formattedMessage) {
    if (!this.logToFile) return;

    try {
      const logFile = this.getCurrentLogFile();
      fs.appendFileSync(logFile, formattedMessage + '\n');
      
      // 检查文件大小，如果超过限制则轮转
      this.rotateLogIfNeeded(logFile);
    } catch (error) {
      console.error('写入日志文件失败:', error);
    }
  }

  /**
   * 日志轮转
   */
  rotateLogIfNeeded(logFile) {
    try {
      const stats = fs.statSync(logFile);
      if (stats.size > this.maxLogSize) {
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        const rotatedFile = logFile.replace('.log', `-${timestamp}.log`);
        fs.renameSync(logFile, rotatedFile);
        
        // 清理旧日志文件
        this.cleanOldLogs();
      }
    } catch (error) {
      console.error('日志轮转失败:', error);
    }
  }

  /**
   * 清理旧日志文件
   */
  cleanOldLogs() {
    try {
      const files = fs.readdirSync(this.logDir)
        .filter(file => file.startsWith('playwright-runner-') && file.endsWith('.log'))
        .map(file => ({
          name: file,
          path: path.join(this.logDir, file),
          mtime: fs.statSync(path.join(this.logDir, file)).mtime
        }))
        .sort((a, b) => b.mtime - a.mtime);

      if (files.length > this.maxLogFiles) {
        const filesToDelete = files.slice(this.maxLogFiles);
        filesToDelete.forEach(file => {
          fs.unlinkSync(file.path);
          console.log(`已删除旧日志文件: ${file.name}`);
        });
      }
    } catch (error) {
      console.error('清理旧日志失败:', error);
    }
  }

  /**
   * 记录日志
   */
  log(level, message, meta = {}) {
    if (this.levels[level] > this.levels[this.logLevel]) {
      return;
    }

    const formattedMessage = this.formatMessage(level, message, meta);
    
    // 输出到控制台
    switch (level) {
      case 'error':
        console.error(formattedMessage);
        break;
      case 'warn':
        console.warn(formattedMessage);
        break;
      case 'debug':
        console.debug(formattedMessage);
        break;
      default:
        console.log(formattedMessage);
    }

    // 写入文件
    this.writeToFile(formattedMessage);
  }

  /**
   * 错误日志
   */
  error(message, meta = {}) {
    this.log('error', message, meta);
  }

  /**
   * 警告日志
   */
  warn(message, meta = {}) {
    this.log('warn', message, meta);
  }

  /**
   * 信息日志
   */
  info(message, meta = {}) {
    this.log('info', message, meta);
  }

  /**
   * 调试日志
   */
  debug(message, meta = {}) {
    this.log('debug', message, meta);
  }

  /**
   * 跟踪日志
   */
  trace(message, meta = {}) {
    this.log('trace', message, meta);
  }

  /**
   * 记录步骤开始
   */
  stepStart(stepId, stepName, code) {
    this.info(`步骤开始执行`, {
      stepId,
      stepName,
      codeLength: code.length,
      timestamp: new Date().toISOString()
    });
  }

  /**
   * 记录步骤完成
   */
  stepComplete(stepId, stepName, duration, result) {
    this.info(`步骤执行完成`, {
      stepId,
      stepName,
      duration,
      resultType: typeof result,
      timestamp: new Date().toISOString()
    });
  }

  /**
   * 记录步骤失败
   */
  stepFailed(stepId, stepName, error, duration) {
    this.error(`步骤执行失败`, {
      stepId,
      stepName,
      error: error.message,
      stack: error.stack,
      duration,
      timestamp: new Date().toISOString()
    });
  }

  /**
   * 记录浏览器操作
   */
  browserAction(action, details = {}) {
    this.debug(`浏览器操作: ${action}`, {
      action,
      ...details,
      timestamp: new Date().toISOString()
    });
  }

  /**
   * 记录性能指标
   */
  performance(metric, value, unit = 'ms') {
    this.info(`性能指标: ${metric}`, {
      metric,
      value,
      unit,
      timestamp: new Date().toISOString()
    });
  }

  /**
   * 记录代码更新
   */
  codeUpdate(stepId, oldCodeHash, newCodeHash) {
    this.info(`代码已更新`, {
      stepId,
      oldCodeHash,
      newCodeHash,
      timestamp: new Date().toISOString()
    });
  }

  /**
   * 获取日志统计
   */
  getLogStats() {
    if (!this.logToFile) {
      return { message: '文件日志未启用' };
    }

    try {
      const logFile = this.getCurrentLogFile();
      if (!fs.existsSync(logFile)) {
        return { message: '日志文件不存在' };
      }

      const stats = fs.statSync(logFile);
      const content = fs.readFileSync(logFile, 'utf8');
      const lines = content.split('\n').filter(line => line.trim());
      
      const levelCounts = {
        ERROR: 0,
        WARN: 0,
        INFO: 0,
        DEBUG: 0,
        TRACE: 0
      };

      lines.forEach(line => {
        Object.keys(levelCounts).forEach(level => {
          if (line.includes(`[${level}]`)) {
            levelCounts[level]++;
          }
        });
      });

      return {
        file: logFile,
        size: stats.size,
        lines: lines.length,
        created: stats.birthtime,
        modified: stats.mtime,
        levelCounts
      };
    } catch (error) {
      return { error: error.message };
    }
  }

  /**
   * 清空当前日志文件
   */
  clearCurrentLog() {
    if (!this.logToFile) return;

    try {
      const logFile = this.getCurrentLogFile();
      if (fs.existsSync(logFile)) {
        fs.writeFileSync(logFile, '');
        this.info('日志文件已清空');
      }
    } catch (error) {
      console.error('清空日志文件失败:', error);
    }
  }

  /**
   * 设置日志级别
   */
  setLogLevel(level) {
    if (this.levels.hasOwnProperty(level)) {
      this.logLevel = level;
      this.info(`日志级别已设置为: ${level}`);
    } else {
      this.warn(`无效的日志级别: ${level}`);
    }
  }

  /**
   * 创建子日志器
   */
  child(prefix) {
    const childLogger = Object.create(this);
    const originalLog = this.log.bind(this);
    
    childLogger.log = (level, message, meta = {}) => {
      originalLog(level, `[${prefix}] ${message}`, meta);
    };

    return childLogger;
  }
}

// 创建默认日志器实例
const defaultLogger = new Logger({
  logLevel: 'info',
  logToFile: true
});

module.exports = {
  Logger,
  logger: defaultLogger
};
