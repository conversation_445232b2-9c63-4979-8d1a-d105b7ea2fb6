/* 全局样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #1e1e1e;
    color: #d4d4d4;
    overflow: hidden;
}

.app-container {
    height: 100vh;
    display: flex;
    flex-direction: column;
}

/* 工具栏样式 */
.toolbar {
    background-color: #2d2d30;
    border-bottom: 1px solid #3e3e42;
    padding: 10px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 60px;
}

.toolbar h1 {
    font-size: 18px;
    color: #ffffff;
    display: flex;
    align-items: center;
    gap: 10px;
}

.toolbar h1 i {
    color: #007acc;
}

.status-indicator {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 5px 12px;
    background-color: #3c3c3c;
    border-radius: 15px;
}

.status-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background-color: #ff6b6b;
    animation: pulse 2s infinite;
}

.status-dot.connected {
    background-color: #51cf66;
}

.status-dot.running {
    background-color: #ffd43b;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

.toolbar-right {
    display: flex;
    gap: 10px;
}

/* 按钮样式 */
.btn {
    padding: 8px 16px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    display: flex;
    align-items: center;
    gap: 6px;
    transition: all 0.2s ease;
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

.btn-primary {
    background-color: #007acc;
    color: white;
}

.btn-primary:hover {
    background-color: #005a9e;
}

.btn-success {
    background-color: #28a745;
    color: white;
}

.btn-success:hover {
    background-color: #218838;
}

.btn-warning {
    background-color: #ffc107;
    color: #212529;
}

.btn-warning:hover {
    background-color: #e0a800;
}

.btn-danger {
    background-color: #dc3545;
    color: white;
}

.btn-danger:hover {
    background-color: #c82333;
}

.btn-info {
    background-color: #17a2b8;
    color: white;
}

.btn-info:hover {
    background-color: #138496;
}

.btn-secondary {
    background-color: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background-color: #5a6268;
}

.btn-sm {
    padding: 4px 8px;
    font-size: 12px;
}

/* 主内容区域 */
.main-content {
    flex: 1;
    display: flex;
    overflow: hidden;
}

.left-panel {
    width: 60%;
    background-color: #252526;
    border-right: 1px solid #3e3e42;
    display: flex;
    flex-direction: column;
}

.right-panel {
    width: 40%;
    background-color: #2d2d30;
    display: flex;
    flex-direction: column;
    overflow-y: auto;
}

/* 面板头部 */
.panel-header {
    background-color: #3c3c3c;
    padding: 10px 15px;
    border-bottom: 1px solid #3e3e42;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.panel-header h3 {
    font-size: 14px;
    color: #cccccc;
    display: flex;
    align-items: center;
    gap: 8px;
}

.panel-actions {
    display: flex;
    gap: 5px;
}

/* 代码编辑器 */
.code-editor-container {
    flex: 1;
    position: relative;
}

#codeEditor {
    height: 100%;
    width: 100%;
}

.code-controls {
    background-color: #2d2d30;
    padding: 15px;
    border-top: 1px solid #3e3e42;
}

.input-group {
    margin-bottom: 10px;
}

.input-group label {
    display: block;
    margin-bottom: 5px;
    font-size: 12px;
    color: #cccccc;
}

.input-group input {
    width: 100%;
    padding: 8px;
    background-color: #3c3c3c;
    border: 1px solid #3e3e42;
    border-radius: 4px;
    color: #d4d4d4;
    font-size: 14px;
}

.input-group input:focus {
    outline: none;
    border-color: #007acc;
}

/* 面板部分 */
.panel-section {
    border-bottom: 1px solid #3e3e42;
}

.panel-section:last-child {
    border-bottom: none;
    flex: 1;
}

/* 步骤容器 */
.steps-container {
    max-height: 300px;
    overflow-y: auto;
}

.step-item {
    padding: 10px 15px;
    border-bottom: 1px solid #3e3e42;
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.step-item:hover {
    background-color: #3c3c3c;
}

.step-item.active {
    background-color: #094771;
    border-left: 3px solid #007acc;
}

.step-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 5px;
}

.step-name {
    font-weight: bold;
    color: #ffffff;
}

.step-status {
    padding: 2px 6px;
    border-radius: 10px;
    font-size: 10px;
    text-transform: uppercase;
}

.step-status.completed {
    background-color: #28a745;
    color: white;
}

.step-status.failed {
    background-color: #dc3545;
    color: white;
}

.step-status.pending {
    background-color: #6c757d;
    color: white;
}

.step-description {
    font-size: 12px;
    color: #cccccc;
    margin-bottom: 5px;
}

.step-actions {
    display: flex;
    gap: 5px;
}

/* 状态容器 */
.status-container {
    padding: 15px;
}

.status-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 8px;
    padding: 5px 0;
    border-bottom: 1px solid #3e3e42;
}

.status-label {
    font-size: 12px;
    color: #cccccc;
}

.status-value {
    font-size: 12px;
    color: #ffffff;
    font-weight: bold;
}

/* 日志面板 */
.log-panel {
    height: 200px;
    background-color: #1e1e1e;
    border-top: 1px solid #3e3e42;
    display: flex;
    flex-direction: column;
}

.log-container {
    flex: 1;
    padding: 10px;
    overflow-y: auto;
    font-family: 'Courier New', monospace;
    font-size: 12px;
    line-height: 1.4;
}

.log-entry {
    margin-bottom: 5px;
    padding: 5px;
    border-radius: 3px;
}

.log-entry.info {
    color: #17a2b8;
}

.log-entry.success {
    color: #28a745;
}

.log-entry.error {
    color: #dc3545;
    background-color: rgba(220, 53, 69, 0.1);
}

.log-entry.warning {
    color: #ffc107;
}

.log-timestamp {
    color: #6c757d;
    margin-right: 10px;
}

/* 模态框样式 */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
}

.modal.show {
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-content {
    background-color: #2d2d30;
    border-radius: 8px;
    width: 400px;
    max-width: 90%;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.modal-header {
    padding: 15px 20px;
    border-bottom: 1px solid #3e3e42;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h3 {
    color: #ffffff;
    font-size: 16px;
}

.modal-close {
    background: none;
    border: none;
    color: #cccccc;
    font-size: 24px;
    cursor: pointer;
}

.modal-close:hover {
    color: #ffffff;
}

.modal-body {
    padding: 20px;
}

.modal-footer {
    padding: 15px 20px;
    border-top: 1px solid #3e3e42;
    display: flex;
    justify-content: flex-end;
    gap: 10px;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .main-content {
        flex-direction: column;
    }
    
    .left-panel,
    .right-panel {
        width: 100%;
    }
    
    .toolbar {
        flex-direction: column;
        height: auto;
        gap: 10px;
    }
    
    .toolbar-right {
        width: 100%;
        justify-content: center;
    }
}
