<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Playwright 动态执行系统</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/monaco-editor/0.44.0/min/vs/loader.min.js"></script>
</head>
<body>
    <div class="app-container">
        <!-- 顶部工具栏 -->
        <header class="toolbar">
            <div class="toolbar-left">
                <h1><i class="fas fa-robot"></i> Playwright 动态执行系统</h1>
            </div>
            <div class="toolbar-center">
                <div class="status-indicator" id="statusIndicator">
                    <span class="status-dot"></span>
                    <span class="status-text">未连接</span>
                </div>
            </div>
            <div class="toolbar-right">
                <button class="btn btn-primary" id="initBrowserBtn">
                    <i class="fas fa-play"></i> 初始化浏览器
                </button>
                <button class="btn btn-warning" id="resetBtn">
                    <i class="fas fa-refresh"></i> 重置
                </button>
                <button class="btn btn-danger" id="shutdownBtn">
                    <i class="fas fa-power-off"></i> 关闭
                </button>
            </div>
        </header>

        <!-- 主内容区域 -->
        <main class="main-content">
            <!-- 左侧面板 -->
            <div class="left-panel">
                <div class="panel-header">
                    <h3><i class="fas fa-code"></i> 代码编辑器</h3>
                    <div class="panel-actions">
                        <button class="btn btn-sm btn-success" id="executeBtn">
                            <i class="fas fa-play"></i> 执行
                        </button>
                        <button class="btn btn-sm btn-info" id="saveStepBtn">
                            <i class="fas fa-save"></i> 保存步骤
                        </button>
                    </div>
                </div>
                <div class="code-editor-container">
                    <div id="codeEditor"></div>
                </div>
                <div class="code-controls">
                    <div class="input-group">
                        <label for="stepName">步骤名称:</label>
                        <input type="text" id="stepName" placeholder="输入步骤名称">
                    </div>
                    <div class="input-group">
                        <label for="stepDescription">步骤描述:</label>
                        <input type="text" id="stepDescription" placeholder="输入步骤描述">
                    </div>
                </div>
            </div>

            <!-- 右侧面板 -->
            <div class="right-panel">
                <!-- 步骤管理 -->
                <div class="panel-section">
                    <div class="panel-header">
                        <h3><i class="fas fa-list"></i> 步骤管理</h3>
                        <button class="btn btn-sm btn-primary" id="refreshStepsBtn">
                            <i class="fas fa-refresh"></i>
                        </button>
                    </div>
                    <div class="steps-container" id="stepsContainer">
                        <!-- 步骤列表将在这里动态生成 -->
                    </div>
                </div>

                <!-- 系统状态 -->
                <div class="panel-section">
                    <div class="panel-header">
                        <h3><i class="fas fa-chart-line"></i> 系统状态</h3>
                    </div>
                    <div class="status-container" id="statusContainer">
                        <div class="status-item">
                            <span class="status-label">浏览器状态:</span>
                            <span class="status-value" id="browserStatus">未初始化</span>
                        </div>
                        <div class="status-item">
                            <span class="status-label">总步骤数:</span>
                            <span class="status-value" id="totalSteps">0</span>
                        </div>
                        <div class="status-item">
                            <span class="status-label">已完成:</span>
                            <span class="status-value" id="completedSteps">0</span>
                        </div>
                        <div class="status-item">
                            <span class="status-label">失败:</span>
                            <span class="status-value" id="failedSteps">0</span>
                        </div>
                    </div>
                </div>
            </div>
        </main>

        <!-- 底部日志面板 -->
        <footer class="log-panel">
            <div class="panel-header">
                <h3><i class="fas fa-terminal"></i> 执行日志</h3>
                <div class="panel-actions">
                    <button class="btn btn-sm btn-secondary" id="clearLogBtn">
                        <i class="fas fa-trash"></i> 清空
                    </button>
                    <button class="btn btn-sm btn-secondary" id="toggleLogBtn">
                        <i class="fas fa-chevron-down"></i>
                    </button>
                </div>
            </div>
            <div class="log-container" id="logContainer">
                <!-- 日志内容将在这里显示 -->
            </div>
        </footer>
    </div>

    <!-- 模态框 -->
    <div class="modal" id="initModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>初始化浏览器</h3>
                <button class="modal-close" id="closeModal">&times;</button>
            </div>
            <div class="modal-body">
                <div class="input-group">
                    <label for="initialUrl">初始URL:</label>
                    <input type="url" id="initialUrl" value="https://www.baidu.com" placeholder="输入要打开的网址">
                </div>
                <div class="input-group">
                    <label>
                        <input type="checkbox" id="headlessMode"> 无头模式
                    </label>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" id="cancelInit">取消</button>
                <button class="btn btn-primary" id="confirmInit">确认初始化</button>
            </div>
        </div>
    </div>

    <!-- 加载脚本 -->
    <script src="js/api.js"></script>
    <script src="js/components/browser-controller.js"></script>
    <script src="js/components/code-editor.js"></script>
    <script src="js/components/step-manager.js"></script>
    <script src="js/components/log-viewer.js"></script>
    <script src="js/app.js"></script>
</body>
</html>
