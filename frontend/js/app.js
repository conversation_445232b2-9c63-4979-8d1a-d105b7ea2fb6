/**
 * 主应用程序
 * 负责初始化和协调各个组件
 */
class App {
    constructor() {
        this.components = {};
        this.statusCheckInterval = null;
        this.init();
    }

    /**
     * 初始化应用
     */
    async init() {
        try {
            // 显示加载状态
            this.showLoading();

            // 初始化组件
            await this.initComponents();

            // 绑定全局事件
            this.bindGlobalEvents();

            // 开始状态检查
            this.startStatusCheck();

            // 隐藏加载状态
            this.hideLoading();

            window.logViewer.addLog('success', '应用初始化完成');
        } catch (error) {
            console.error('应用初始化失败:', error);
            this.showError('应用初始化失败: ' + error.message);
        }
    }

    /**
     * 初始化组件
     */
    async initComponents() {
        // 初始化日志查看器（最先初始化，其他组件需要用到）
        window.logViewer = new LogViewer();
        this.components.logViewer = window.logViewer;

        // 初始化浏览器控制器
        window.browserController = new BrowserController();
        this.components.browserController = window.browserController;

        // 初始化代码编辑器
        window.codeEditor = new CodeEditor();
        this.components.codeEditor = window.codeEditor;

        // 初始化步骤管理器
        window.stepManager = new StepManager();
        this.components.stepManager = window.stepManager;

        window.logViewer.addLog('info', '所有组件初始化完成');
    }

    /**
     * 绑定全局事件
     */
    bindGlobalEvents() {
        // 窗口大小变化时重新布局
        window.addEventListener('resize', () => {
            this.handleResize();
        });

        // 页面卸载前清理
        window.addEventListener('beforeunload', () => {
            this.cleanup();
        });

        // 键盘快捷键
        document.addEventListener('keydown', (e) => {
            this.handleKeyboard(e);
        });

        // 全局错误处理
        window.addEventListener('error', (e) => {
            window.logViewer.addLog('error', `全局错误: ${e.message}`, {
                filename: e.filename,
                lineno: e.lineno,
                colno: e.colno
            });
        });

        // 未处理的Promise拒绝
        window.addEventListener('unhandledrejection', (e) => {
            window.logViewer.addLog('error', `未处理的Promise拒绝: ${e.reason}`);
        });
    }

    /**
     * 处理键盘事件
     */
    handleKeyboard(e) {
        // Ctrl+Shift+C 清空日志
        if (e.ctrlKey && e.shiftKey && e.code === 'KeyC') {
            e.preventDefault();
            window.logViewer.clearLogs();
        }

        // Ctrl+Shift+E 导出日志
        if (e.ctrlKey && e.shiftKey && e.code === 'KeyE') {
            e.preventDefault();
            window.logViewer.exportLogs();
        }

        // Ctrl+Shift+R 刷新步骤
        if (e.ctrlKey && e.shiftKey && e.code === 'KeyR') {
            e.preventDefault();
            window.stepManager.refreshSteps();
        }

        // F5 刷新状态
        if (e.code === 'F5') {
            e.preventDefault();
            this.refreshStatus();
        }

        // Escape 关闭模态框
        if (e.code === 'Escape') {
            const modal = document.querySelector('.modal.show');
            if (modal) {
                modal.classList.remove('show');
            }
        }
    }

    /**
     * 处理窗口大小变化
     */
    handleResize() {
        // Monaco编辑器自动调整大小
        if (window.codeEditor && window.codeEditor.editor) {
            window.codeEditor.editor.layout();
        }
    }

    /**
     * 开始状态检查
     */
    startStatusCheck() {
        // 立即检查一次
        this.checkStatus();

        // 每5秒检查一次状态
        this.statusCheckInterval = setInterval(() => {
            this.checkStatus();
        }, 5000);
    }

    /**
     * 停止状态检查
     */
    stopStatusCheck() {
        if (this.statusCheckInterval) {
            clearInterval(this.statusCheckInterval);
            this.statusCheckInterval = null;
        }
    }

    /**
     * 检查系统状态
     */
    async checkStatus() {
        try {
            const connected = await window.browserController.checkConnection();
            if (connected) {
                await window.stepManager.updateSystemStatus();
            }
        } catch (error) {
            // 静默处理连接错误，避免日志过多
        }
    }

    /**
     * 刷新状态
     */
    async refreshStatus() {
        window.logViewer.addLog('info', '正在刷新系统状态...');
        
        try {
            await this.checkStatus();
            await window.stepManager.refreshSteps();
            window.logViewer.addLog('success', '系统状态刷新完成');
        } catch (error) {
            window.logViewer.addLog('error', `状态刷新失败: ${error.message}`);
        }
    }

    /**
     * 显示加载状态
     */
    showLoading() {
        const loading = document.createElement('div');
        loading.id = 'appLoading';
        loading.innerHTML = `
            <div style="
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background-color: rgba(30, 30, 30, 0.9);
                display: flex;
                align-items: center;
                justify-content: center;
                z-index: 9999;
                color: white;
                font-size: 18px;
            ">
                <div style="text-align: center;">
                    <i class="fas fa-spinner fa-spin" style="font-size: 48px; margin-bottom: 20px;"></i>
                    <div>正在初始化应用...</div>
                </div>
            </div>
        `;
        document.body.appendChild(loading);
    }

    /**
     * 隐藏加载状态
     */
    hideLoading() {
        const loading = document.getElementById('appLoading');
        if (loading) {
            loading.remove();
        }
    }

    /**
     * 显示错误信息
     */
    showError(message) {
        const error = document.createElement('div');
        error.innerHTML = `
            <div style="
                position: fixed;
                top: 20px;
                right: 20px;
                background-color: #dc3545;
                color: white;
                padding: 15px 20px;
                border-radius: 5px;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
                z-index: 10000;
                max-width: 400px;
            ">
                <div style="display: flex; align-items: center; gap: 10px;">
                    <i class="fas fa-exclamation-triangle"></i>
                    <span>${message}</span>
                    <button onclick="this.parentElement.parentElement.remove()" style="
                        background: none;
                        border: none;
                        color: white;
                        font-size: 18px;
                        cursor: pointer;
                        margin-left: auto;
                    ">&times;</button>
                </div>
            </div>
        `;
        document.body.appendChild(error);

        // 5秒后自动移除
        setTimeout(() => {
            if (error.parentElement) {
                error.remove();
            }
        }, 5000);
    }

    /**
     * 显示成功信息
     */
    showSuccess(message) {
        const success = document.createElement('div');
        success.innerHTML = `
            <div style="
                position: fixed;
                top: 20px;
                right: 20px;
                background-color: #28a745;
                color: white;
                padding: 15px 20px;
                border-radius: 5px;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
                z-index: 10000;
                max-width: 400px;
            ">
                <div style="display: flex; align-items: center; gap: 10px;">
                    <i class="fas fa-check-circle"></i>
                    <span>${message}</span>
                    <button onclick="this.parentElement.parentElement.remove()" style="
                        background: none;
                        border: none;
                        color: white;
                        font-size: 18px;
                        cursor: pointer;
                        margin-left: auto;
                    ">&times;</button>
                </div>
            </div>
        `;
        document.body.appendChild(success);

        // 3秒后自动移除
        setTimeout(() => {
            if (success.parentElement) {
                success.remove();
            }
        }, 3000);
    }

    /**
     * 清理资源
     */
    cleanup() {
        this.stopStatusCheck();
        
        // 清理组件
        Object.values(this.components).forEach(component => {
            if (component.cleanup && typeof component.cleanup === 'function') {
                component.cleanup();
            }
        });
    }

    /**
     * 获取应用信息
     */
    getAppInfo() {
        return {
            version: '1.0.0',
            components: Object.keys(this.components),
            uptime: Date.now() - this.startTime,
            logStats: window.logViewer ? window.logViewer.getLogStats() : null
        };
    }
}

// 当DOM加载完成后初始化应用
document.addEventListener('DOMContentLoaded', () => {
    window.app = new App();
});

// 导出到全局作用域以便调试
window.App = App;
