/**
 * 日志查看组件
 * 负责显示和管理执行日志
 */
class LogViewer {
    constructor() {
        this.logs = [];
        this.maxLogs = 1000; // 最大日志条数
        this.isCollapsed = false;
        this.initElements();
        this.bindEvents();
        this.addLog('info', '日志系统已初始化');
    }

    /**
     * 初始化DOM元素
     */
    initElements() {
        this.logContainer = document.getElementById('logContainer');
        this.clearLogBtn = document.getElementById('clearLogBtn');
        this.toggleLogBtn = document.getElementById('toggleLogBtn');
        this.logPanel = document.querySelector('.log-panel');
    }

    /**
     * 绑定事件
     */
    bindEvents() {
        this.clearLogBtn.addEventListener('click', () => this.clearLogs());
        this.toggleLogBtn.addEventListener('click', () => this.togglePanel());
    }

    /**
     * 添加日志
     */
    addLog(type, message, data = null) {
        const timestamp = new Date().toLocaleTimeString();
        const log = {
            id: Date.now() + Math.random(),
            type,
            message,
            data,
            timestamp
        };

        this.logs.push(log);

        // 限制日志数量
        if (this.logs.length > this.maxLogs) {
            this.logs = this.logs.slice(-this.maxLogs);
        }

        this.renderLog(log);
        this.scrollToBottom();
    }

    /**
     * 渲染单条日志
     */
    renderLog(log) {
        const logElement = document.createElement('div');
        logElement.className = `log-entry ${log.type}`;
        logElement.dataset.logId = log.id;

        let content = `
            <span class="log-timestamp">[${log.timestamp}]</span>
            <span class="log-message">${this.escapeHtml(log.message)}</span>
        `;

        if (log.data) {
            content += `
                <div class="log-data">
                    <pre>${this.escapeHtml(JSON.stringify(log.data, null, 2))}</pre>
                </div>
            `;
        }

        logElement.innerHTML = content;
        this.logContainer.appendChild(logElement);
    }

    /**
     * 清空日志
     */
    clearLogs() {
        this.logs = [];
        this.logContainer.innerHTML = '';
        this.addLog('info', '日志已清空');
    }

    /**
     * 切换面板显示/隐藏
     */
    togglePanel() {
        this.isCollapsed = !this.isCollapsed;
        
        if (this.isCollapsed) {
            this.logPanel.style.height = '40px';
            this.logContainer.style.display = 'none';
            this.toggleLogBtn.querySelector('i').className = 'fas fa-chevron-up';
        } else {
            this.logPanel.style.height = '200px';
            this.logContainer.style.display = 'block';
            this.toggleLogBtn.querySelector('i').className = 'fas fa-chevron-down';
            this.scrollToBottom();
        }
    }

    /**
     * 滚动到底部
     */
    scrollToBottom() {
        if (!this.isCollapsed) {
            setTimeout(() => {
                this.logContainer.scrollTop = this.logContainer.scrollHeight;
            }, 10);
        }
    }

    /**
     * 添加成功日志
     */
    success(message, data = null) {
        this.addLog('success', message, data);
    }

    /**
     * 添加错误日志
     */
    error(message, data = null) {
        this.addLog('error', message, data);
    }

    /**
     * 添加警告日志
     */
    warning(message, data = null) {
        this.addLog('warning', message, data);
    }

    /**
     * 添加信息日志
     */
    info(message, data = null) {
        this.addLog('info', message, data);
    }

    /**
     * 根据类型获取日志
     */
    getLogsByType(type) {
        return this.logs.filter(log => log.type === type);
    }

    /**
     * 获取最近的日志
     */
    getRecentLogs(count = 10) {
        return this.logs.slice(-count);
    }

    /**
     * 搜索日志
     */
    searchLogs(query) {
        return this.logs.filter(log => 
            log.message.toLowerCase().includes(query.toLowerCase()) ||
            (log.data && JSON.stringify(log.data).toLowerCase().includes(query.toLowerCase()))
        );
    }

    /**
     * 导出日志
     */
    exportLogs() {
        const logText = this.logs.map(log => {
            let line = `[${log.timestamp}] [${log.type.toUpperCase()}] ${log.message}`;
            if (log.data) {
                line += `\n${JSON.stringify(log.data, null, 2)}`;
            }
            return line;
        }).join('\n\n');

        const blob = new Blob([logText], { type: 'text/plain' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `playwright-logs-${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.txt`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);

        this.addLog('info', '日志已导出');
    }

    /**
     * 获取日志统计
     */
    getLogStats() {
        const stats = {
            total: this.logs.length,
            info: 0,
            success: 0,
            warning: 0,
            error: 0
        };

        this.logs.forEach(log => {
            if (stats.hasOwnProperty(log.type)) {
                stats[log.type]++;
            }
        });

        return stats;
    }

    /**
     * HTML转义
     */
    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    /**
     * 格式化时间戳
     */
    formatTimestamp(timestamp) {
        return new Date(timestamp).toLocaleString();
    }

    /**
     * 设置最大日志数量
     */
    setMaxLogs(maxLogs) {
        this.maxLogs = maxLogs;
        if (this.logs.length > this.maxLogs) {
            this.logs = this.logs.slice(-this.maxLogs);
            this.renderAllLogs();
        }
    }

    /**
     * 重新渲染所有日志
     */
    renderAllLogs() {
        this.logContainer.innerHTML = '';
        this.logs.forEach(log => this.renderLog(log));
        this.scrollToBottom();
    }

    /**
     * 过滤日志显示
     */
    filterLogs(types = ['info', 'success', 'warning', 'error']) {
        const logEntries = this.logContainer.querySelectorAll('.log-entry');
        
        logEntries.forEach(entry => {
            const logType = entry.className.split(' ').find(cls => 
                ['info', 'success', 'warning', 'error'].includes(cls)
            );
            
            if (types.includes(logType)) {
                entry.style.display = 'block';
            } else {
                entry.style.display = 'none';
            }
        });
    }

    /**
     * 高亮搜索结果
     */
    highlightSearch(query) {
        const logEntries = this.logContainer.querySelectorAll('.log-entry');
        
        logEntries.forEach(entry => {
            const messageElement = entry.querySelector('.log-message');
            if (messageElement) {
                const originalText = messageElement.textContent;
                if (query && originalText.toLowerCase().includes(query.toLowerCase())) {
                    const regex = new RegExp(`(${query})`, 'gi');
                    const highlightedText = originalText.replace(regex, '<mark>$1</mark>');
                    messageElement.innerHTML = highlightedText;
                    entry.style.display = 'block';
                } else if (query) {
                    entry.style.display = 'none';
                } else {
                    messageElement.textContent = originalText;
                    entry.style.display = 'block';
                }
            }
        });
    }
}
