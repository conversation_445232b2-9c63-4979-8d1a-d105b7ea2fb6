/**
 * 浏览器控制组件
 * 负责浏览器的初始化、重置和关闭操作
 */
class BrowserController {
    constructor() {
        this.isInitialized = false;
        this.initElements();
        this.bindEvents();
    }

    /**
     * 初始化DOM元素
     */
    initElements() {
        this.initBrowserBtn = document.getElementById('initBrowserBtn');
        this.resetBtn = document.getElementById('resetBtn');
        this.shutdownBtn = document.getElementById('shutdownBtn');
        this.statusIndicator = document.getElementById('statusIndicator');
        this.statusDot = this.statusIndicator.querySelector('.status-dot');
        this.statusText = this.statusIndicator.querySelector('.status-text');
        
        // 模态框元素
        this.initModal = document.getElementById('initModal');
        this.closeModal = document.getElementById('closeModal');
        this.cancelInit = document.getElementById('cancelInit');
        this.confirmInit = document.getElementById('confirmInit');
        this.initialUrl = document.getElementById('initialUrl');
        this.headlessMode = document.getElementById('headlessMode');
    }

    /**
     * 绑定事件
     */
    bindEvents() {
        this.initBrowserBtn.addEventListener('click', () => this.showInitModal());
        this.resetBtn.addEventListener('click', () => this.resetBrowser());
        this.shutdownBtn.addEventListener('click', () => this.shutdownService());
        
        // 模态框事件
        this.closeModal.addEventListener('click', () => this.hideInitModal());
        this.cancelInit.addEventListener('click', () => this.hideInitModal());
        this.confirmInit.addEventListener('click', () => this.initBrowser());
        
        // 点击模态框外部关闭
        this.initModal.addEventListener('click', (e) => {
            if (e.target === this.initModal) {
                this.hideInitModal();
            }
        });
    }

    /**
     * 显示初始化模态框
     */
    showInitModal() {
        this.initModal.classList.add('show');
    }

    /**
     * 隐藏初始化模态框
     */
    hideInitModal() {
        this.initModal.classList.remove('show');
    }

    /**
     * 初始化浏览器
     */
    async initBrowser() {
        try {
            this.setLoading(this.confirmInit, true);
            
            const options = {
                url: this.initialUrl.value || 'https://www.baidu.com',
                headless: this.headlessMode.checked
            };

            window.logViewer.addLog('info', `正在初始化浏览器... URL: ${options.url}, 无头模式: ${options.headless}`);
            
            const result = await window.api.initBrowser(options);
            
            if (result.success) {
                this.isInitialized = true;
                this.updateStatus('running', '浏览器运行中');
                this.updateButtonStates();
                window.logViewer.addLog('success', '浏览器初始化成功');
                window.logViewer.addLog('info', `浏览器信息: ${JSON.stringify(result.browserInfo, null, 2)}`);
                this.hideInitModal();
                
                // 通知其他组件浏览器已初始化
                window.dispatchEvent(new CustomEvent('browserInitialized', { detail: result }));
            } else {
                throw new Error(result.message || '初始化失败');
            }
        } catch (error) {
            window.logViewer.addLog('error', `浏览器初始化失败: ${error.message}`);
            this.updateStatus('disconnected', '初始化失败');
        } finally {
            this.setLoading(this.confirmInit, false);
        }
    }

    /**
     * 重置浏览器
     */
    async resetBrowser() {
        if (!confirm('确定要重置浏览器状态吗？这将清除所有步骤和浏览器状态。')) {
            return;
        }

        try {
            this.setLoading(this.resetBtn, true);
            window.logViewer.addLog('info', '正在重置浏览器状态...');
            
            const result = await window.api.reset();
            
            if (result.success) {
                window.logViewer.addLog('success', '浏览器状态重置成功');
                
                // 通知其他组件浏览器已重置
                window.dispatchEvent(new CustomEvent('browserReset'));
                
                // 刷新步骤列表
                if (window.stepManager) {
                    window.stepManager.refreshSteps();
                }
            } else {
                throw new Error(result.message || '重置失败');
            }
        } catch (error) {
            window.logViewer.addLog('error', `重置失败: ${error.message}`);
        } finally {
            this.setLoading(this.resetBtn, false);
        }
    }

    /**
     * 关闭服务
     */
    async shutdownService() {
        if (!confirm('确定要关闭服务吗？这将终止整个应用程序。')) {
            return;
        }

        try {
            this.setLoading(this.shutdownBtn, true);
            window.logViewer.addLog('info', '正在关闭服务...');
            
            const result = await window.api.shutdown();
            
            if (result.success) {
                window.logViewer.addLog('success', '服务关闭成功');
                this.updateStatus('disconnected', '服务已关闭');
                this.isInitialized = false;
                this.updateButtonStates();
            } else {
                throw new Error(result.message || '关闭失败');
            }
        } catch (error) {
            window.logViewer.addLog('error', `关闭服务失败: ${error.message}`);
        } finally {
            this.setLoading(this.shutdownBtn, false);
        }
    }

    /**
     * 更新状态指示器
     */
    updateStatus(status, text) {
        this.statusDot.className = `status-dot ${status}`;
        this.statusText.textContent = text;
    }

    /**
     * 更新按钮状态
     */
    updateButtonStates() {
        this.initBrowserBtn.disabled = this.isInitialized;
        this.resetBtn.disabled = !this.isInitialized;
    }

    /**
     * 设置按钮加载状态
     */
    setLoading(button, loading) {
        if (loading) {
            button.disabled = true;
            const icon = button.querySelector('i');
            if (icon) {
                icon.className = 'fas fa-spinner fa-spin';
            }
        } else {
            button.disabled = false;
            const icon = button.querySelector('i');
            if (icon) {
                // 恢复原始图标
                if (button === this.initBrowserBtn) {
                    icon.className = 'fas fa-play';
                } else if (button === this.resetBtn) {
                    icon.className = 'fas fa-refresh';
                } else if (button === this.shutdownBtn) {
                    icon.className = 'fas fa-power-off';
                } else if (button === this.confirmInit) {
                    icon.className = 'fas fa-check';
                }
            }
        }
    }

    /**
     * 检查服务器连接状态
     */
    async checkConnection() {
        try {
            const result = await window.api.health();
            if (result.status === 'ok') {
                if (result.isRunning) {
                    this.isInitialized = true;
                    this.updateStatus('running', '浏览器运行中');
                } else {
                    this.isInitialized = false;
                    this.updateStatus('connected', '已连接');
                }
                this.updateButtonStates();
                return true;
            }
        } catch (error) {
            this.updateStatus('disconnected', '连接失败');
            this.isInitialized = false;
            this.updateButtonStates();
            return false;
        }
    }
}
