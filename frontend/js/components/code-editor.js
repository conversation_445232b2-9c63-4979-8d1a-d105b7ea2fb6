/**
 * 代码编辑器组件
 * 基于 Monaco Editor 的代码编辑和执行功能
 */
class CodeEditor {
    constructor() {
        this.editor = null;
        this.currentStepId = null;
        this.initElements();
        this.initEditor();
        this.bindEvents();
    }

    /**
     * 初始化DOM元素
     */
    initElements() {
        this.executeBtn = document.getElementById('executeBtn');
        this.saveStepBtn = document.getElementById('saveStepBtn');
        this.stepName = document.getElementById('stepName');
        this.stepDescription = document.getElementById('stepDescription');
    }

    /**
     * 初始化Monaco编辑器
     */
    async initEditor() {
        // 等待Monaco加载完成
        await new Promise((resolve) => {
            require.config({ paths: { vs: 'https://cdnjs.cloudflare.com/ajax/libs/monaco-editor/0.44.0/min/vs' } });
            require(['vs/editor/editor.main'], resolve);
        });

        // 创建编辑器实例
        this.editor = monaco.editor.create(document.getElementById('codeEditor'), {
            value: this.getDefaultCode(),
            language: 'javascript',
            theme: 'vs-dark',
            fontSize: 14,
            minimap: { enabled: false },
            scrollBeyondLastLine: false,
            automaticLayout: true,
            wordWrap: 'on',
            lineNumbers: 'on',
            folding: true,
            selectOnLineNumbers: true,
            roundedSelection: false,
            readOnly: false,
            cursorStyle: 'line',
            glyphMargin: true,
            contextmenu: true,
            mouseWheelZoom: true,
            quickSuggestions: {
                other: true,
                comments: true,
                strings: true
            },
            parameterHints: {
                enabled: true
            },
            autoIndent: 'full',
            formatOnPaste: true,
            formatOnType: true
        });

        // 添加Playwright API的智能提示
        this.addPlaywrightCompletions();
        
        // 添加快捷键
        this.addKeyboardShortcuts();
    }

    /**
     * 获取默认代码
     */
    getDefaultCode() {
        return `// Playwright 自动化代码示例
// 可用的全局变量: page, context, browser

// 导航到网页
await page.goto('https://www.baidu.com');

// 等待页面加载
await page.waitForLoadState('networkidle');

// 获取页面标题
const title = await page.title();
console.log('页面标题:', title);

// 返回结果
return { title, url: page.url() };`;
    }

    /**
     * 添加Playwright API智能提示
     */
    addPlaywrightCompletions() {
        monaco.languages.registerCompletionItemProvider('javascript', {
            provideCompletionItems: (model, position) => {
                const suggestions = [
                    {
                        label: 'page.goto',
                        kind: monaco.languages.CompletionItemKind.Method,
                        insertText: 'page.goto(${1:url})',
                        insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
                        documentation: '导航到指定URL'
                    },
                    {
                        label: 'page.click',
                        kind: monaco.languages.CompletionItemKind.Method,
                        insertText: 'page.click(${1:selector})',
                        insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
                        documentation: '点击指定元素'
                    },
                    {
                        label: 'page.fill',
                        kind: monaco.languages.CompletionItemKind.Method,
                        insertText: 'page.fill(${1:selector}, ${2:value})',
                        insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
                        documentation: '填写输入框'
                    },
                    {
                        label: 'page.waitForSelector',
                        kind: monaco.languages.CompletionItemKind.Method,
                        insertText: 'page.waitForSelector(${1:selector})',
                        insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
                        documentation: '等待元素出现'
                    },
                    {
                        label: 'page.textContent',
                        kind: monaco.languages.CompletionItemKind.Method,
                        insertText: 'page.textContent(${1:selector})',
                        insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
                        documentation: '获取元素文本内容'
                    },
                    {
                        label: 'page.screenshot',
                        kind: monaco.languages.CompletionItemKind.Method,
                        insertText: 'page.screenshot({ path: ${1:\'screenshot.png\'} })',
                        insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
                        documentation: '截取页面截图'
                    }
                ];
                return { suggestions };
            }
        });
    }

    /**
     * 添加键盘快捷键
     */
    addKeyboardShortcuts() {
        // Ctrl+Enter 执行代码
        this.editor.addCommand(monaco.KeyMod.CtrlCmd | monaco.KeyCode.Enter, () => {
            this.executeCode();
        });

        // Ctrl+S 保存步骤
        this.editor.addCommand(monaco.KeyMod.CtrlCmd | monaco.KeyCode.KeyS, () => {
            this.saveStep();
        });
    }

    /**
     * 绑定事件
     */
    bindEvents() {
        this.executeBtn.addEventListener('click', () => this.executeCode());
        this.saveStepBtn.addEventListener('click', () => this.saveStep());

        // 监听浏览器状态变化
        window.addEventListener('browserInitialized', () => {
            this.updateButtonStates(true);
        });

        window.addEventListener('browserReset', () => {
            this.updateButtonStates(false);
        });
    }

    /**
     * 执行代码
     */
    async executeCode() {
        const code = this.editor.getValue();
        if (!code.trim()) {
            window.logViewer.addLog('warning', '代码不能为空');
            return;
        }

        try {
            this.setLoading(this.executeBtn, true);
            
            const stepName = this.stepName.value || `步骤_${Date.now()}`;
            const description = this.stepDescription.value || '自动生成的步骤';

            window.logViewer.addLog('info', `正在执行步骤: ${stepName}`);
            window.logViewer.addLog('info', `代码:\n${code}`);

            let result;
            if (this.currentStepId) {
                // 更新现有步骤并执行
                result = await window.api.updateAndExecute(code, this.currentStepId, stepName, description);
            } else {
                // 执行新步骤
                result = await window.api.executeStep(code, stepName, description);
            }

            if (result.success) {
                window.logViewer.addLog('success', `步骤执行成功 (ID: ${result.stepId})`);
                if (result.result) {
                    window.logViewer.addLog('info', `执行结果:\n${JSON.stringify(result.result, null, 2)}`);
                }
                
                // 刷新步骤列表
                if (window.stepManager) {
                    window.stepManager.refreshSteps();
                }
                
                // 清空输入框
                this.stepName.value = '';
                this.stepDescription.value = '';
                this.currentStepId = null;
            } else {
                throw new Error(result.message || '执行失败');
            }
        } catch (error) {
            window.logViewer.addLog('error', `代码执行失败: ${error.message}`);
        } finally {
            this.setLoading(this.executeBtn, false);
        }
    }

    /**
     * 保存步骤（不执行）
     */
    async saveStep() {
        const code = this.editor.getValue();
        const stepName = this.stepName.value || `步骤_${Date.now()}`;
        const description = this.stepDescription.value || '';

        if (!code.trim()) {
            window.logViewer.addLog('warning', '代码不能为空');
            return;
        }

        try {
            this.setLoading(this.saveStepBtn, true);
            
            window.logViewer.addLog('info', `正在保存步骤: ${stepName}`);
            
            const result = await window.api.addStep(stepName, description, code);
            
            if (result.success) {
                window.logViewer.addLog('success', `步骤保存成功: ${stepName}`);
                
                // 刷新步骤列表
                if (window.stepManager) {
                    window.stepManager.refreshSteps();
                }
                
                // 清空输入框
                this.stepName.value = '';
                this.stepDescription.value = '';
            } else {
                throw new Error(result.message || '保存失败');
            }
        } catch (error) {
            window.logViewer.addLog('error', `保存步骤失败: ${error.message}`);
        } finally {
            this.setLoading(this.saveStepBtn, false);
        }
    }

    /**
     * 加载步骤代码到编辑器
     */
    loadStep(step) {
        this.editor.setValue(step.code || '');
        this.stepName.value = step.name || '';
        this.stepDescription.value = step.description || '';
        this.currentStepId = step.id;
        
        window.logViewer.addLog('info', `已加载步骤: ${step.name}`);
    }

    /**
     * 清空编辑器
     */
    clear() {
        this.editor.setValue(this.getDefaultCode());
        this.stepName.value = '';
        this.stepDescription.value = '';
        this.currentStepId = null;
    }

    /**
     * 更新按钮状态
     */
    updateButtonStates(browserInitialized) {
        this.executeBtn.disabled = !browserInitialized;
    }

    /**
     * 设置按钮加载状态
     */
    setLoading(button, loading) {
        if (loading) {
            button.disabled = true;
            const icon = button.querySelector('i');
            if (icon) {
                icon.className = 'fas fa-spinner fa-spin';
            }
        } else {
            button.disabled = false;
            const icon = button.querySelector('i');
            if (icon) {
                if (button === this.executeBtn) {
                    icon.className = 'fas fa-play';
                } else if (button === this.saveStepBtn) {
                    icon.className = 'fas fa-save';
                }
            }
        }
    }
}
