/**
 * 步骤管理组件
 * 负责显示、管理和操作执行步骤
 */
class StepManager {
    constructor() {
        this.steps = [];
        this.activeStepId = null;
        this.initElements();
        this.bindEvents();
        this.refreshSteps();
    }

    /**
     * 初始化DOM元素
     */
    initElements() {
        this.stepsContainer = document.getElementById('stepsContainer');
        this.refreshStepsBtn = document.getElementById('refreshStepsBtn');
        this.totalSteps = document.getElementById('totalSteps');
        this.completedSteps = document.getElementById('completedSteps');
        this.failedSteps = document.getElementById('failedSteps');
        this.browserStatus = document.getElementById('browserStatus');
    }

    /**
     * 绑定事件
     */
    bindEvents() {
        this.refreshStepsBtn.addEventListener('click', () => this.refreshSteps());
        
        // 监听浏览器状态变化
        window.addEventListener('browserInitialized', () => {
            this.updateSystemStatus();
        });

        window.addEventListener('browserReset', () => {
            this.refreshSteps();
            this.updateSystemStatus();
        });
    }

    /**
     * 刷新步骤列表
     */
    async refreshSteps() {
        try {
            this.setLoading(this.refreshStepsBtn, true);
            
            const result = await window.api.getSteps();
            
            if (result.success) {
                this.steps = result.steps || [];
                this.renderSteps();
                this.updateStatistics();
            } else {
                throw new Error(result.message || '获取步骤失败');
            }
        } catch (error) {
            window.logViewer.addLog('error', `刷新步骤列表失败: ${error.message}`);
            this.steps = [];
            this.renderSteps();
        } finally {
            this.setLoading(this.refreshStepsBtn, false);
        }
    }

    /**
     * 渲染步骤列表
     */
    renderSteps() {
        if (this.steps.length === 0) {
            this.stepsContainer.innerHTML = `
                <div class="empty-state">
                    <i class="fas fa-inbox" style="font-size: 48px; color: #6c757d; margin-bottom: 10px;"></i>
                    <p style="color: #6c757d; text-align: center;">暂无步骤</p>
                </div>
            `;
            return;
        }

        this.stepsContainer.innerHTML = this.steps.map(step => this.createStepElement(step)).join('');
        
        // 绑定步骤事件
        this.bindStepEvents();
    }

    /**
     * 创建步骤元素
     */
    createStepElement(step) {
        const statusClass = this.getStatusClass(step.status);
        const statusText = this.getStatusText(step.status);
        const isActive = step.id === this.activeStepId ? 'active' : '';
        
        return `
            <div class="step-item ${isActive}" data-step-id="${step.id}">
                <div class="step-header">
                    <span class="step-name">${this.escapeHtml(step.name)}</span>
                    <span class="step-status ${statusClass}">${statusText}</span>
                </div>
                ${step.description ? `<div class="step-description">${this.escapeHtml(step.description)}</div>` : ''}
                <div class="step-actions">
                    <button class="btn btn-sm btn-info load-step-btn" title="加载到编辑器">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="btn btn-sm btn-success execute-step-btn" title="重新执行">
                        <i class="fas fa-play"></i>
                    </button>
                    <button class="btn btn-sm btn-danger delete-step-btn" title="删除步骤">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
                ${step.result ? `
                    <div class="step-result" style="margin-top: 8px; padding: 8px; background-color: #2d2d30; border-radius: 4px; font-size: 11px;">
                        <strong>执行结果:</strong>
                        <pre style="margin: 4px 0; white-space: pre-wrap; color: #d4d4d4;">${this.escapeHtml(JSON.stringify(step.result, null, 2))}</pre>
                    </div>
                ` : ''}
                ${step.error ? `
                    <div class="step-error" style="margin-top: 8px; padding: 8px; background-color: rgba(220, 53, 69, 0.1); border-radius: 4px; font-size: 11px; color: #dc3545;">
                        <strong>错误信息:</strong>
                        <pre style="margin: 4px 0; white-space: pre-wrap;">${this.escapeHtml(step.error)}</pre>
                    </div>
                ` : ''}
            </div>
        `;
    }

    /**
     * 绑定步骤事件
     */
    bindStepEvents() {
        // 加载步骤到编辑器
        this.stepsContainer.querySelectorAll('.load-step-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.stopPropagation();
                const stepId = e.target.closest('.step-item').dataset.stepId;
                const step = this.steps.find(s => s.id === stepId);
                if (step && window.codeEditor) {
                    window.codeEditor.loadStep(step);
                    this.setActiveStep(stepId);
                }
            });
        });

        // 重新执行步骤
        this.stepsContainer.querySelectorAll('.execute-step-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.stopPropagation();
                const stepId = e.target.closest('.step-item').dataset.stepId;
                const step = this.steps.find(s => s.id === stepId);
                if (step) {
                    this.executeStep(step);
                }
            });
        });

        // 删除步骤
        this.stepsContainer.querySelectorAll('.delete-step-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.stopPropagation();
                const stepId = e.target.closest('.step-item').dataset.stepId;
                const step = this.steps.find(s => s.id === stepId);
                if (step) {
                    this.deleteStep(step);
                }
            });
        });

        // 点击步骤项
        this.stepsContainer.querySelectorAll('.step-item').forEach(item => {
            item.addEventListener('click', () => {
                const stepId = item.dataset.stepId;
                const step = this.steps.find(s => s.id === stepId);
                if (step && window.codeEditor) {
                    window.codeEditor.loadStep(step);
                    this.setActiveStep(stepId);
                }
            });
        });
    }

    /**
     * 设置活动步骤
     */
    setActiveStep(stepId) {
        this.activeStepId = stepId;
        
        // 更新UI
        this.stepsContainer.querySelectorAll('.step-item').forEach(item => {
            item.classList.remove('active');
        });
        
        const activeItem = this.stepsContainer.querySelector(`[data-step-id="${stepId}"]`);
        if (activeItem) {
            activeItem.classList.add('active');
        }
    }

    /**
     * 执行步骤
     */
    async executeStep(step) {
        try {
            window.logViewer.addLog('info', `正在重新执行步骤: ${step.name}`);
            
            const result = await window.api.updateAndExecute(step.code, step.id, step.name, step.description);
            
            if (result.success) {
                window.logViewer.addLog('success', `步骤执行成功: ${step.name}`);
                if (result.result) {
                    window.logViewer.addLog('info', `执行结果:\n${JSON.stringify(result.result, null, 2)}`);
                }
                this.refreshSteps();
            } else {
                throw new Error(result.message || '执行失败');
            }
        } catch (error) {
            window.logViewer.addLog('error', `步骤执行失败: ${error.message}`);
        }
    }

    /**
     * 删除步骤
     */
    async deleteStep(step) {
        if (!confirm(`确定要删除步骤 "${step.name}" 吗？`)) {
            return;
        }

        try {
            window.logViewer.addLog('info', `正在删除步骤: ${step.name}`);
            
            const result = await window.api.deleteStep(step.id);
            
            if (result.success) {
                window.logViewer.addLog('success', `步骤删除成功: ${step.name}`);
                this.refreshSteps();
                
                // 如果删除的是当前活动步骤，清空编辑器
                if (this.activeStepId === step.id && window.codeEditor) {
                    window.codeEditor.clear();
                    this.activeStepId = null;
                }
            } else {
                throw new Error(result.message || '删除失败');
            }
        } catch (error) {
            window.logViewer.addLog('error', `删除步骤失败: ${error.message}`);
        }
    }

    /**
     * 更新统计信息
     */
    updateStatistics() {
        const total = this.steps.length;
        const completed = this.steps.filter(s => s.status === 'completed').length;
        const failed = this.steps.filter(s => s.status === 'failed').length;

        this.totalSteps.textContent = total;
        this.completedSteps.textContent = completed;
        this.failedSteps.textContent = failed;
    }

    /**
     * 更新系统状态
     */
    async updateSystemStatus() {
        try {
            const result = await window.api.getStatus();
            
            if (result.success) {
                this.browserStatus.textContent = result.isRunning ? '运行中' : '未初始化';
                
                // 更新统计信息
                if (result.totalSteps !== undefined) {
                    this.totalSteps.textContent = result.totalSteps;
                    this.completedSteps.textContent = result.completedSteps || 0;
                    this.failedSteps.textContent = result.failedSteps || 0;
                }
            }
        } catch (error) {
            this.browserStatus.textContent = '连接失败';
        }
    }

    /**
     * 获取状态样式类
     */
    getStatusClass(status) {
        switch (status) {
            case 'completed': return 'completed';
            case 'failed': return 'failed';
            default: return 'pending';
        }
    }

    /**
     * 获取状态文本
     */
    getStatusText(status) {
        switch (status) {
            case 'completed': return '已完成';
            case 'failed': return '失败';
            default: return '待执行';
        }
    }

    /**
     * HTML转义
     */
    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    /**
     * 设置按钮加载状态
     */
    setLoading(button, loading) {
        if (loading) {
            button.disabled = true;
            const icon = button.querySelector('i');
            if (icon) {
                icon.className = 'fas fa-spinner fa-spin';
            }
        } else {
            button.disabled = false;
            const icon = button.querySelector('i');
            if (icon) {
                icon.className = 'fas fa-refresh';
            }
        }
    }
}
