/**
 * API 调用封装类
 * 负责与后端服务器的所有通信
 */
class API {
    constructor(baseUrl = 'http://localhost:8081') {
        this.baseUrl = baseUrl;
    }

    /**
     * 通用请求方法
     */
    async request(endpoint, options = {}) {
        const url = `${this.baseUrl}${endpoint}`;
        const config = {
            headers: {
                'Content-Type': 'application/json',
                ...options.headers
            },
            ...options
        };

        try {
            const response = await fetch(url, config);
            const data = await response.json();
            
            if (!response.ok) {
                throw new Error(data.message || `HTTP ${response.status}`);
            }
            
            return data;
        } catch (error) {
            console.error(`API请求失败 [${endpoint}]:`, error);
            throw error;
        }
    }

    /**
     * 健康检查
     */
    async health() {
        return this.request('/health');
    }

    /**
     * 初始化浏览器
     */
    async initBrowser(options = {}) {
        return this.request('/init', {
            method: 'POST',
            body: JSON.stringify({
                headless: options.headless || false,
                url: options.url || 'about:blank'
            })
        });
    }

    /**
     * 执行单个步骤
     */
    async executeStep(code, stepName = '', description = '') {
        return this.request('/execute-step', {
            method: 'POST',
            body: JSON.stringify({
                code,
                stepName,
                description
            })
        });
    }

    /**
     * 更新代码并执行
     */
    async updateAndExecute(code, stepId = null, stepName = '', description = '') {
        return this.request('/update-and-execute', {
            method: 'POST',
            body: JSON.stringify({
                code,
                stepId,
                stepName,
                description
            })
        });
    }

    /**
     * 获取系统状态
     */
    async getStatus() {
        return this.request('/status');
    }

    /**
     * 获取所有步骤
     */
    async getSteps() {
        return this.request('/steps');
    }

    /**
     * 添加新步骤（不执行）
     */
    async addStep(name, description = '', code = '') {
        return this.request('/add-step', {
            method: 'POST',
            body: JSON.stringify({
                name,
                description,
                code
            })
        });
    }

    /**
     * 删除步骤
     */
    async deleteStep(stepId) {
        return this.request(`/steps/${stepId}`, {
            method: 'DELETE'
        });
    }

    /**
     * 重置浏览器状态
     */
    async reset() {
        return this.request('/reset', {
            method: 'POST'
        });
    }

    /**
     * 关闭服务
     */
    async shutdown() {
        return this.request('/shutdown', {
            method: 'POST'
        });
    }
}

// 创建全局API实例
window.api = new API();
