# Playwright 动态执行系统使用指南

## 概述

Playwright 动态执行系统是一个基于 Playwright 自动化技术的创新解决方案，支持边自动化操作边执行的功能。系统允许你在运行时动态更新代码并立即执行，实现真正的"说一步，做一步"的自动化体验。

## 核心特性

- **动态代码执行**: 支持在运行时更新和执行新的自动化代码
- **浏览器状态保持**: 保持浏览器实例运行，无需重复初始化
- **步骤化管理**: 将自动化操作分解为独立的步骤，便于管理和调试
- **热更新支持**: 即使在执行过程中也能更新代码逻辑
- **安全代码验证**: 内置代码安全检查和语法验证
- **详细日志记录**: 完整的执行历史和性能监控

## 快速开始

### 1. 安装依赖

```bash
cd src/views/zpAgent/tools/automatedTesting/components/runner
npm install
npx playwright install
```

### 2. 启动服务

```bash
npm start
```

服务将在 `http://localhost:8081` 启动。

### 3. 初始化浏览器

```bash
curl -X POST http://localhost:8081/init \
  -H "Content-Type: application/json" \
  -d '{
    "headless": false,
    "url": "https://www.baidu.com"
  }'
```

### 4. 执行第一个步骤

```bash
curl -X POST http://localhost:8081/execute-step \
  -H "Content-Type: application/json" \
  -d '{
    "code": "const title = await page.title(); console.log(\"页面标题:\", title); return title;",
    "stepName": "获取页面标题",
    "description": "获取当前页面的标题"
  }'
```

## API 接口文档

### 健康检查
- **GET** `/health`
- 检查服务状态

### 浏览器管理
- **POST** `/init` - 初始化浏览器
- **POST** `/reset` - 重置浏览器状态
- **POST** `/shutdown` - 关闭服务

### 步骤执行
- **POST** `/execute-step` - 执行单个步骤
- **POST** `/update-and-execute` - 更新代码并执行

### 步骤管理
- **GET** `/steps` - 获取所有步骤
- **POST** `/add-step` - 添加新步骤
- **DELETE** `/steps/:stepId` - 删除步骤

### 状态查询
- **GET** `/status` - 获取系统状态

## 代码示例

### 基础操作

```javascript
// 导航到网页
await page.goto('https://example.com');

// 填写表单
await page.fill('#username', '用户名');
await page.fill('#password', '密码');
await page.click('#login-button');

// 等待页面加载
await page.waitForSelector('.dashboard');

// 获取文本内容
const welcomeText = await page.textContent('.welcome-message');
console.log('欢迎信息:', welcomeText);
```

### 条件判断

```javascript
// 检查元素是否存在
const isVisible = await page.isVisible('.error-message');
if (isVisible) {
  const errorText = await page.textContent('.error-message');
  console.log('错误信息:', errorText);
  return { error: errorText };
} else {
  console.log('登录成功');
  return { success: true };
}
```

### 循环操作

```javascript
// 获取所有商品
const products = await page.$$('.product-item');
console.log('找到', products.length, '个商品');

for (let i = 0; i < products.length; i++) {
  const name = await products[i].textContent('.product-name');
  const price = await products[i].textContent('.product-price');
  console.log(`商品 ${i + 1}: ${name} - ${price}`);
}
```

## 高级功能

### 多页面操作

```javascript
// 创建新页面
const newPage = await context.newPage();
await newPage.goto('https://example.com/new-page');

// 在新页面中操作
await newPage.click('.special-button');

// 切换回原页面
await page.bringToFront();

// 关闭新页面
await newPage.close();
```

### 文件操作

```javascript
// 上传文件
await page.setInputFiles('input[type="file"]', '/path/to/file.txt');

// 下载文件
const [download] = await Promise.all([
  page.waitForEvent('download'),
  page.click('.download-link')
]);
await download.saveAs('./downloaded-file.pdf');
```

### 性能监控

```javascript
const startTime = Date.now();
await page.goto('https://example.com');
await page.waitForLoadState('networkidle');
const loadTime = Date.now() - startTime;
console.log('页面加载时间:', loadTime, 'ms');
```

## 最佳实践

### 1. 错误处理

```javascript
try {
  await page.waitForSelector('.target-element', { timeout: 5000 });
  await page.click('.target-element');
} catch (error) {
  console.log('元素未找到，执行备选方案');
  await page.click('.alternative-element');
}
```

### 2. 等待策略

```javascript
// 推荐：等待特定元素
await page.waitForSelector('.content-loaded');

// 避免：使用固定时间等待
// await page.waitForTimeout(3000); // 不推荐
```

### 3. 选择器优化

```javascript
// 推荐：使用稳定的选择器
await page.click('[data-testid="submit-button"]');

// 避免：使用易变的选择器
// await page.click('.btn.btn-primary.submit'); // 不推荐
```

## 故障排除

### 常见问题

1. **浏览器初始化失败**
   - 检查 Playwright 是否正确安装
   - 确认系统支持所需的浏览器

2. **代码执行超时**
   - 增加超时时间设置
   - 检查网络连接状态

3. **元素定位失败**
   - 使用浏览器开发者工具验证选择器
   - 添加适当的等待条件

### 调试技巧

1. **使用截图调试**
```javascript
await page.screenshot({ path: 'debug.png' });
```

2. **输出页面信息**
```javascript
console.log('当前URL:', page.url());
console.log('页面标题:', await page.title());
```

3. **检查元素状态**
```javascript
console.log('元素可见:', await page.isVisible('.target'));
console.log('元素启用:', await page.isEnabled('.target'));
```

## 安全注意事项

1. **代码验证**: 系统会自动验证代码安全性
2. **权限控制**: 限制可访问的系统资源
3. **输入过滤**: 过滤危险的代码模式
4. **执行沙箱**: 在受控环境中执行代码

## 性能优化

1. **复用浏览器实例**: 避免频繁创建和销毁浏览器
2. **合理使用等待**: 选择合适的等待策略
3. **批量操作**: 将相关操作组合在一个步骤中
4. **资源清理**: 及时关闭不需要的页面和资源

## 扩展开发

系统采用模块化设计，支持自定义扩展：

- **自定义执行器**: 扩展 PlaywrightExecutor 类
- **自定义验证器**: 扩展 CodeUpdater 类
- **自定义日志器**: 扩展 Logger 类

## 技术支持

如有问题或建议，请联系开发团队或查看项目文档。
