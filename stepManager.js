/**
 * 步骤管理器
 * 负责管理执行步骤的增删改查和状态跟踪
 */
class StepManager {
  constructor() {
    this.steps = new Map();
    this.executionOrder = [];
    this.currentStepIndex = -1;
  }

  /**
   * 生成唯一ID
   */
  generateId() {
    return `step_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 添加步骤
   * @param {Object} stepData - 步骤数据
   * @param {string} stepData.name - 步骤名称
   * @param {string} stepData.description - 步骤描述
   * @param {string} stepData.code - 步骤代码
   * @param {number} stepData.order - 执行顺序（可选）
   */
  addStep(stepData) {
    const id = this.generateId();
    const step = {
      id,
      name: stepData.name || `步骤_${id}`,
      description: stepData.description || '',
      code: stepData.code || '',
      status: 'pending', // pending, running, completed, failed, skipped
      result: null,
      error: null,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      executedAt: null,
      duration: null,
      order: stepData.order || this.executionOrder.length
    };

    this.steps.set(id, step);
    this.executionOrder.push(id);

    console.log(`步骤已添加: ${step.name} (${id})`);
    return step;
  }

  /**
   * 获取步骤
   * @param {string} stepId - 步骤ID
   */
  getStep(stepId) {
    return this.steps.get(stepId);
  }

  /**
   * 获取所有步骤
   */
  getAllSteps() {
    return this.executionOrder.map(id => this.steps.get(id)).filter(Boolean);
  }

  /**
   * 更新步骤
   * @param {string} stepId - 步骤ID
   * @param {Object} updateData - 更新数据
   */
  updateStep(stepId, updateData) {
    const step = this.steps.get(stepId);
    if (!step) {
      throw new Error(`步骤不存在: ${stepId}`);
    }

    // 更新允许的字段
    const allowedFields = ['name', 'description', 'code', 'order'];
    allowedFields.forEach(field => {
      if (updateData.hasOwnProperty(field)) {
        step[field] = updateData[field];
      }
    });

    step.updatedAt = new Date().toISOString();
    this.steps.set(stepId, step);

    console.log(`步骤已更新: ${step.name} (${stepId})`);
    return step;
  }

  /**
   * 更新步骤状态
   * @param {string} stepId - 步骤ID
   * @param {string} status - 新状态
   * @param {any} result - 执行结果（可选）
   * @param {Error} error - 错误信息（可选）
   */
  updateStepStatus(stepId, status, result = null, error = null) {
    const step = this.steps.get(stepId);
    if (!step) {
      throw new Error(`步骤不存在: ${stepId}`);
    }

    const oldStatus = step.status;
    step.status = status;
    step.updatedAt = new Date().toISOString();

    if (status === 'running') {
      step.executedAt = new Date().toISOString();
      step.startTime = Date.now();
    }

    if (status === 'completed' || status === 'failed') {
      if (step.startTime) {
        step.duration = Date.now() - step.startTime;
      }
      
      if (result !== null) {
        step.result = result;
      }
      
      if (error) {
        step.error = {
          message: error.message,
          stack: error.stack,
          timestamp: new Date().toISOString()
        };
      }
    }

    this.steps.set(stepId, step);
    console.log(`步骤状态更新: ${step.name} (${stepId}) ${oldStatus} -> ${status}`);
    
    return step;
  }

  /**
   * 删除步骤
   * @param {string} stepId - 步骤ID
   */
  removeStep(stepId) {
    const step = this.steps.get(stepId);
    if (!step) {
      return false;
    }

    this.steps.delete(stepId);
    const index = this.executionOrder.indexOf(stepId);
    if (index > -1) {
      this.executionOrder.splice(index, 1);
    }

    // 调整当前步骤索引
    if (index <= this.currentStepIndex) {
      this.currentStepIndex--;
    }

    console.log(`步骤已删除: ${step.name} (${stepId})`);
    return true;
  }

  /**
   * 清空所有步骤
   */
  clearSteps() {
    const count = this.steps.size;
    this.steps.clear();
    this.executionOrder = [];
    this.currentStepIndex = -1;
    console.log(`已清空 ${count} 个步骤`);
  }

  /**
   * 获取下一个待执行的步骤
   */
  getNextStep() {
    for (let i = this.currentStepIndex + 1; i < this.executionOrder.length; i++) {
      const step = this.steps.get(this.executionOrder[i]);
      if (step && step.status === 'pending') {
        return step;
      }
    }
    return null;
  }

  /**
   * 获取当前步骤
   */
  getCurrentStep() {
    if (this.currentStepIndex >= 0 && this.currentStepIndex < this.executionOrder.length) {
      return this.steps.get(this.executionOrder[this.currentStepIndex]);
    }
    return null;
  }

  /**
   * 移动到下一步
   */
  moveToNextStep() {
    this.currentStepIndex++;
    return this.getCurrentStep();
  }

  /**
   * 重置执行位置
   */
  resetExecution() {
    this.currentStepIndex = -1;
    // 重置所有步骤状态为 pending
    this.steps.forEach(step => {
      if (step.status !== 'pending') {
        step.status = 'pending';
        step.result = null;
        step.error = null;
        step.executedAt = null;
        step.duration = null;
        step.updatedAt = new Date().toISOString();
      }
    });
    console.log('执行位置已重置');
  }

  /**
   * 获取步骤统计信息
   */
  getStatistics() {
    const steps = this.getAllSteps();
    const stats = {
      total: steps.length,
      pending: 0,
      running: 0,
      completed: 0,
      failed: 0,
      skipped: 0,
      totalDuration: 0,
      averageDuration: 0
    };

    let completedWithDuration = 0;

    steps.forEach(step => {
      stats[step.status]++;
      if (step.duration) {
        stats.totalDuration += step.duration;
        completedWithDuration++;
      }
    });

    if (completedWithDuration > 0) {
      stats.averageDuration = Math.round(stats.totalDuration / completedWithDuration);
    }

    return stats;
  }

  /**
   * 按状态获取步骤
   * @param {string} status - 步骤状态
   */
  getStepsByStatus(status) {
    return this.getAllSteps().filter(step => step.status === status);
  }

  /**
   * 搜索步骤
   * @param {string} keyword - 搜索关键词
   */
  searchSteps(keyword) {
    if (!keyword) {
      return this.getAllSteps();
    }

    const lowerKeyword = keyword.toLowerCase();
    return this.getAllSteps().filter(step => 
      step.name.toLowerCase().includes(lowerKeyword) ||
      step.description.toLowerCase().includes(lowerKeyword) ||
      step.code.toLowerCase().includes(lowerKeyword)
    );
  }

  /**
   * 重新排序步骤
   * @param {Array} newOrder - 新的步骤ID顺序
   */
  reorderSteps(newOrder) {
    // 验证新顺序包含所有现有步骤
    if (newOrder.length !== this.executionOrder.length) {
      throw new Error('新顺序必须包含所有现有步骤');
    }

    for (const stepId of newOrder) {
      if (!this.steps.has(stepId)) {
        throw new Error(`步骤不存在: ${stepId}`);
      }
    }

    this.executionOrder = [...newOrder];
    
    // 更新步骤的 order 字段
    this.executionOrder.forEach((stepId, index) => {
      const step = this.steps.get(stepId);
      if (step) {
        step.order = index;
        step.updatedAt = new Date().toISOString();
      }
    });

    console.log('步骤顺序已重新排列');
  }

  /**
   * 复制步骤
   * @param {string} stepId - 要复制的步骤ID
   */
  duplicateStep(stepId) {
    const originalStep = this.steps.get(stepId);
    if (!originalStep) {
      throw new Error(`步骤不存在: ${stepId}`);
    }

    const duplicatedStep = this.addStep({
      name: `${originalStep.name} (副本)`,
      description: originalStep.description,
      code: originalStep.code
    });

    console.log(`步骤已复制: ${originalStep.name} -> ${duplicatedStep.name}`);
    return duplicatedStep;
  }

  /**
   * 导出步骤数据
   */
  exportSteps() {
    return {
      steps: this.getAllSteps(),
      executionOrder: this.executionOrder,
      statistics: this.getStatistics(),
      exportedAt: new Date().toISOString()
    };
  }

  /**
   * 导入步骤数据
   * @param {Object} data - 导入的数据
   */
  importSteps(data) {
    if (!data.steps || !Array.isArray(data.steps)) {
      throw new Error('无效的导入数据格式');
    }

    this.clearSteps();

    data.steps.forEach(stepData => {
      this.addStep({
        name: stepData.name,
        description: stepData.description,
        code: stepData.code
      });
    });

    console.log(`已导入 ${data.steps.length} 个步骤`);
  }
}

module.exports = StepManager;
