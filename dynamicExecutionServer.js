const express = require('express');
const cors = require('cors');
const PlaywrightExecutor = require('./playwrightExecutor');
const StepManager = require('./stepManager');
const CodeUpdater = require('./codeUpdater');

/**
 * 动态执行服务器
 * 支持边自动化操作边执行的功能
 * 可以在运行时更新代码并执行新的步骤
 */
class DynamicExecutionServer {
  constructor(port = 8081) {
    this.port = port;
    this.app = express();
    this.playwrightExecutor = new PlaywrightExecutor();
    this.stepManager = new StepManager();
    this.codeUpdater = new CodeUpdater();
    this.isRunning = false;
    
    this.setupMiddleware();
    this.setupRoutes();
  }

  /**
   * 设置中间件
   */
  setupMiddleware() {
    this.app.use(cors());
    this.app.use(express.json({ limit: '10mb' }));
    this.app.use(express.urlencoded({ extended: true }));
    
    // 请求日志中间件
    this.app.use((req, res, next) => {
      console.log(`[${new Date().toISOString()}] ${req.method} ${req.path}`);
      next();
    });
  }

  /**
   * 设置路由
   */
  setupRoutes() {
    // 健康检查
    this.app.get('/health', (req, res) => {
      res.json({
        status: 'ok',
        timestamp: new Date().toISOString(),
        isRunning: this.isRunning
      });
    });

    // 初始化浏览器
    this.app.post('/init', async (req, res) => {
      try {
        const { headless = false, url = 'about:blank' } = req.body;
        await this.playwrightExecutor.init(headless, url);
        this.isRunning = true;
        res.json({
          success: true,
          message: '浏览器初始化成功',
          browserInfo: await this.playwrightExecutor.getBrowserInfo()
        });
      } catch (error) {
        console.error('初始化失败:', error);
        res.status(500).json({
          success: false,
          message: '浏览器初始化失败',
          error: error.message
        });
      }
    });

    // 执行单个步骤
    this.app.post('/execute-step', async (req, res) => {
      try {
        const { code, stepName, description } = req.body;
        
        if (!code) {
          return res.status(400).json({
            success: false,
            message: '代码不能为空'
          });
        }

        // 验证代码
        const validation = this.codeUpdater.validateCode(code);
        if (!validation.valid) {
          return res.status(400).json({
            success: false,
            message: '代码验证失败',
            errors: validation.errors
          });
        }

        // 添加步骤到管理器
        const step = this.stepManager.addStep({
          name: stepName || `步骤_${Date.now()}`,
          description: description || '自动生成的步骤',
          code: code
        });

        // 执行步骤
        const result = await this.playwrightExecutor.executeStep(code, step.id);
        
        // 更新步骤状态
        this.stepManager.updateStepStatus(step.id, 'completed', result);

        res.json({
          success: true,
          message: '步骤执行成功',
          stepId: step.id,
          result: result
        });

      } catch (error) {
        console.error('步骤执行失败:', error);
        res.status(500).json({
          success: false,
          message: '步骤执行失败',
          error: error.message,
          stack: error.stack
        });
      }
    });

    // 更新代码并执行
    this.app.post('/update-and-execute', async (req, res) => {
      try {
        const { code, stepId, stepName, description } = req.body;
        
        // 更新代码
        const updateResult = this.codeUpdater.updateCode(code);
        if (!updateResult.success) {
          return res.status(400).json(updateResult);
        }

        let targetStepId = stepId;
        
        // 如果没有指定步骤ID，创建新步骤
        if (!targetStepId) {
          const step = this.stepManager.addStep({
            name: stepName || `更新步骤_${Date.now()}`,
            description: description || '动态更新的步骤',
            code: code
          });
          targetStepId = step.id;
        } else {
          // 更新现有步骤
          this.stepManager.updateStep(targetStepId, { code });
        }

        // 执行更新后的代码
        const result = await this.playwrightExecutor.executeStep(code, targetStepId);
        
        // 更新步骤状态
        this.stepManager.updateStepStatus(targetStepId, 'completed', result);

        res.json({
          success: true,
          message: '代码更新并执行成功',
          stepId: targetStepId,
          result: result
        });

      } catch (error) {
        console.error('代码更新执行失败:', error);
        res.status(500).json({
          success: false,
          message: '代码更新执行失败',
          error: error.message
        });
      }
    });

    // 获取当前状态
    this.app.get('/status', async (req, res) => {
      try {
        const browserInfo = this.isRunning ? await this.playwrightExecutor.getBrowserInfo() : null;
        const steps = this.stepManager.getAllSteps();
        
        res.json({
          success: true,
          isRunning: this.isRunning,
          browserInfo: browserInfo,
          totalSteps: steps.length,
          completedSteps: steps.filter(s => s.status === 'completed').length,
          failedSteps: steps.filter(s => s.status === 'failed').length,
          steps: steps
        });
      } catch (error) {
        console.error('获取状态失败:', error);
        res.status(500).json({
          success: false,
          message: '获取状态失败',
          error: error.message
        });
      }
    });

    // 获取所有步骤
    this.app.get('/steps', (req, res) => {
      try {
        const steps = this.stepManager.getAllSteps();
        res.json({
          success: true,
          steps: steps
        });
      } catch (error) {
        console.error('获取步骤列表失败:', error);
        res.status(500).json({
          success: false,
          message: '获取步骤列表失败',
          error: error.message
        });
      }
    });

    // 添加新步骤（不执行）
    this.app.post('/add-step', (req, res) => {
      try {
        const { name, description, code } = req.body;
        
        const step = this.stepManager.addStep({
          name: name || `步骤_${Date.now()}`,
          description: description || '',
          code: code || ''
        });

        res.json({
          success: true,
          message: '步骤添加成功',
          step: step
        });
      } catch (error) {
        console.error('添加步骤失败:', error);
        res.status(500).json({
          success: false,
          message: '添加步骤失败',
          error: error.message
        });
      }
    });

    // 删除步骤
    this.app.delete('/steps/:stepId', (req, res) => {
      try {
        const { stepId } = req.params;
        const success = this.stepManager.removeStep(stepId);
        
        if (success) {
          res.json({
            success: true,
            message: '步骤删除成功'
          });
        } else {
          res.status(404).json({
            success: false,
            message: '步骤不存在'
          });
        }
      } catch (error) {
        console.error('删除步骤失败:', error);
        res.status(500).json({
          success: false,
          message: '删除步骤失败',
          error: error.message
        });
      }
    });

    // 重置浏览器状态
    this.app.post('/reset', async (req, res) => {
      try {
        await this.playwrightExecutor.reset();
        this.stepManager.clearSteps();
        
        res.json({
          success: true,
          message: '浏览器状态重置成功'
        });
      } catch (error) {
        console.error('重置失败:', error);
        res.status(500).json({
          success: false,
          message: '重置失败',
          error: error.message
        });
      }
    });

    // 关闭服务
    this.app.post('/shutdown', async (req, res) => {
      try {
        await this.playwrightExecutor.close();
        this.isRunning = false;
        
        res.json({
          success: true,
          message: '服务关闭成功'
        });
        
        // 延迟关闭服务器
        setTimeout(() => {
          process.exit(0);
        }, 1000);
      } catch (error) {
        console.error('关闭服务失败:', error);
        res.status(500).json({
          success: false,
          message: '关闭服务失败',
          error: error.message
        });
      }
    });
  }

  /**
   * 启动服务器
   */
  start() {
    return new Promise((resolve, reject) => {
      try {
        this.server = this.app.listen(this.port, () => {
          console.log(`动态执行服务器已启动，监听端口 ${this.port}`);
          console.log(`API 文档:`);
          console.log(`  - 健康检查: GET http://localhost:${this.port}/health`);
          console.log(`  - 初始化浏览器: POST http://localhost:${this.port}/init`);
          console.log(`  - 执行步骤: POST http://localhost:${this.port}/execute-step`);
          console.log(`  - 更新并执行: POST http://localhost:${this.port}/update-and-execute`);
          console.log(`  - 获取状态: GET http://localhost:${this.port}/status`);
          console.log(`  - 获取步骤: GET http://localhost:${this.port}/steps`);
          console.log(`  - 重置: POST http://localhost:${this.port}/reset`);
          console.log(`  - 关闭: POST http://localhost:${this.port}/shutdown`);
          resolve();
        });
      } catch (error) {
        reject(error);
      }
    });
  }

  /**
   * 停止服务器
   */
  async stop() {
    if (this.server) {
      await this.playwrightExecutor.close();
      this.server.close();
      this.isRunning = false;
      console.log('动态执行服务器已停止');
    }
  }
}

module.exports = DynamicExecutionServer;
